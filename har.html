<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HAR文件上传解析器</title>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .title {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .subtitle {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 40px;
        }

        .upload-area {
            position: relative;
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 60px 20px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .upload-area.dragover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4em;
            color: #ddd;
            margin-bottom: 20px;
            transition: color 0.3s ease;
        }

        .upload-area:hover .upload-icon {
            color: #667eea;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 0.9em;
            color: #999;
        }

        #fileInput {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            z-index: 10;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            min-width: 120px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-container {
            display: none;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .result-container {
            display: none;
            margin-top: 30px;
            padding: 0;
            text-align: left;
        }

        .result-success {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #38bdf8;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(14, 165, 233, 0.2);
            padding: 25px;
            overflow: hidden;
        }

        .result-error {
            background: linear-gradient(135deg, #fef2f2 0%, #fde8e8 100%);
            border: 2px solid #f87171;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(239, 68, 68, 0.2);
            padding: 25px;
            overflow: hidden;
        }

        .result-title {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .result-success .result-title {
            color: #0ea5e9;
        }

        .result-error .result-title {
            color: #dc2626;
            font-weight: bold;
            font-size: 1.3em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .result-error .error-message {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 15px;
            color: #7f1d1d;
            font-size: 1em;
            line-height: 1.5;
            border-left: 4px solid #ef4444;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
        }

        .file-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .method-stats, .status-stats {
            margin-top: 15px;
        }

        .stats-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .stats-item {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .data-field {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
        }

        .field-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 8px;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .field-value {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 15px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            word-break: break-all;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .copy-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 0.8em;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .copy-btn:hover {
            background: #218838;
        }

        .copy-btn.copied {
            background: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🚀 HAR文件解析器</h1>
        <p class="subtitle">上传您的HAR文件，快速解析HTTP请求数据</p>
        <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border: 2px solid #f59e0b; border-radius: 15px; padding: 15px; margin: 20px 0; text-align: center;">
            <strong style="color: #92400e; font-size: 1.1em;">⚠️ 重要提示：仅支持 Stream 导出的 HAR 文件</strong>
        </div>
        
        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">📁</div>
            <div class="upload-text">点击选择文件或拖拽到此处</div>
            <div class="upload-hint">仅支持 Stream 导出的 .har 文件，最大10MB，选择后自动解析</div>
            <input type="file" id="fileInput" accept=".har" />
        </div>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
        
        <button class="btn" id="uploadBtn" disabled style="display: none;">
            <span id="btnText">请选择文件</span>
        </button>
        
        <button class="btn" id="clearBtn" style="display: none;">清除结果</button>
        
        <div class="result-container" id="resultContainer">
            <!-- 结果将在这里显示 -->
        </div>
    </div>

    <script>
        // 配置变量 - 部署时修改这里
        // const API_BASE_URL = 'https://ckeck.orionnn.me/api';
        const API_BASE_URL = 'http://localhost:18888/api';
        const UPLOAD_ENDPOINT = '/common/upload';
        
        // 完整的上传接口地址
        const UPLOAD_URL = API_BASE_URL + UPLOAD_ENDPOINT;
        
        let selectedFile = null;
        
        $(document).ready(function() {
            initializeUpload();
        });

        function initializeUpload() {
            const $fileInput = $('#fileInput');
            const $uploadArea = $('#uploadArea');
            const $uploadBtn = $('#uploadBtn');
            const $clearBtn = $('#clearBtn');

            // 文件选择事件
            $fileInput.on('change', function(e) {
                handleFileSelect(e.target.files[0]);
            });
            
            // 拖拽事件
            $uploadArea.on('dragenter', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('dragover');
            });
            
            $uploadArea.on('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('dragover');
            });
            
            $uploadArea.on('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const rect = this.getBoundingClientRect();
                const x = e.originalEvent.clientX;
                const y = e.originalEvent.clientY;
                
                if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
                    $(this).removeClass('dragover');
                }
            });
            
            $uploadArea.on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');
                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelect(files[0]);
                }
            });
            
            // 上传按钮事件
            $uploadBtn.on('click', function() {
                if (selectedFile) {
                    uploadFile();
                }
            });
            
            // 清除按钮事件
            $clearBtn.on('click', function() {
                clearResults();
            });
        }
        
        function handleFileSelect(file) {
            if (!file) return;

            // 验证文件类型
            if (!file.name.toLowerCase().endsWith('.har')) {
                showError('请选择 Stream 类型的 HAR 格式文件！');
                return;
            }

            // 验证文件大小 (10MB)
            if (file.size > 10 * 1024 * 1024) {
                showError('文件大小不能超过10MB！');
                return;
            }

            selectedFile = file;
            updateUploadButton();
            hideResults();

            // 自动上传文件
            uploadFile();
        }
        
        function updateUploadButton() {
            const $uploadBtn = $('#uploadBtn');
            const $btnText = $('#btnText');
            
            if (selectedFile) {
                $uploadBtn.prop('disabled', false);
                $btnText.text(`上传 ${selectedFile.name}`);
            } else {
                $uploadBtn.prop('disabled', true);
                $btnText.text('请选择文件');
            }
        }
        
        function uploadFile() {
            if (!selectedFile) return;
            
            const formData = new FormData();
            formData.append('file', selectedFile);
            
            // 显示进度条
            showProgress();
            
            // 禁用按钮
            $('#uploadBtn').prop('disabled', true);
            $('#btnText').html('<span class="loading"></span>解析中...');
            
            $.ajax({
                url: UPLOAD_URL,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener('progress', function(e) {
                        if (e.lengthComputable) {
                            const percentComplete = (e.loaded / e.total) * 100;
                            updateProgress(percentComplete);
                        }
                    });
                    return xhr;
                },
                success: function(response) {
                    hideProgress();
                    if (response.success) {
                        showSuccess(response.data, response.message);
                    } else {
                        showError(response.message || '上传失败');
                    }
                },
                error: function(xhr, status, error) {
                    hideProgress();
                    let errorMsg = '网络错误，请检查服务器连接';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    showError(errorMsg);
                },
                complete: function() {
                    // 恢复按钮状态
                    updateUploadButton();
                    $('#clearBtn').show();
                }
            });
        }
        
        function showProgress() {
            $('#progressContainer').show();
            updateProgress(0);
        }
        
        function hideProgress() {
            $('#progressContainer').hide();
        }
        
        function updateProgress(percent) {
            $('#progressFill').css('width', percent + '%');
        }
        
        function showSuccess(data, message) {
            const $container = $('#resultContainer');

            let html = `
                <div class="result-success">
                    <div class="result-title">✅ ${message}</div>

                    <div class="data-field">
                        <button class="copy-btn" onclick="copyToClipboard('result-data', this)">复制</button>
                        <div class="field-label">解析结果</div>
                        <div class="field-value" id="result-data">${escapeHtml(data)}</div>
                    </div>
                </div>
            `;

            $container.html(html).show();
        }
        
        function showError(message) {
            const $container = $('#resultContainer');
            const html = `
                <div class="result-error">
                    <div class="result-title">
                        <span style="font-size: 1.5em;">⚠️</span>
                        解析失败
                    </div>
                    <div class="error-message">
                        <strong>错误详情：</strong><br>
                        ${message}
                        <br><br>
                        <small>💡 请检查HAR文件格式是否正确，或尝试重新抓包</small>
                    </div>
                </div>
            `;
            $container.html(html).show();
        }
        
        function hideResults() {
            $('#resultContainer').hide();
            $('#clearBtn').hide();
        }
        
        function clearResults() {
            selectedFile = null;
            $('#fileInput').val('');
            updateUploadButton();
            hideResults();
            hideProgress();
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyToClipboard(elementId, button) {
            const element = document.getElementById(elementId);
            const text = element.textContent;

            navigator.clipboard.writeText(text).then(function() {
                // 显示复制成功
                const originalText = button.textContent;
                button.textContent = '已复制';
                button.classList.add('copied');

                setTimeout(function() {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败: ', err);
                // 降级方案
                fallbackCopyTextToClipboard(text, button);
            });
        }

        function copyAllData(data) {
            navigator.clipboard.writeText(data).then(function() {
                showMessage('全部数据已复制到剪贴板');
            }).catch(function(err) {
                console.error('复制失败: ', err);
                fallbackCopyTextToClipboard(data);
            });
        }

        function fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful && button) {
                    const originalText = button.textContent;
                    button.textContent = '已复制';
                    button.classList.add('copied');

                    setTimeout(function() {
                        button.textContent = originalText;
                        button.classList.remove('copied');
                    }, 2000);
                }
            } catch (err) {
                console.error('降级复制也失败了: ', err);
            }

            document.body.removeChild(textArea);
        }

        function showMessage(msg) {
            // 创建临时消息提示
            const messageDiv = document.createElement('div');
            messageDiv.textContent = msg;
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 9999;
                font-size: 14px;
            `;

            document.body.appendChild(messageDiv);

            setTimeout(function() {
                document.body.removeChild(messageDiv);
            }, 3000);
        }
    </script>
</body>
</html>
