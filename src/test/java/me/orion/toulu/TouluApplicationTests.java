package me.orion.toulu;

import com.alibaba.fastjson2.JSONArray;
import jakarta.annotation.Resource;
import me.orion.toulu.service.HarParseService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileInputStream;
import java.io.FileNotFoundException;

@SpringBootTest
class TouluApplicationTests {

    @Resource
    private HarParseService harParseService;


    @Test
    void contextLoads() throws Exception {
        JSONArray entries = JSONArray.from(harParseService.parseHarFile(new FileInputStream("C:\\Users\\<USER>\\Desktop\\klg.har")).get("entries"));
        System.out.println(entries);
    }

}
