package me.orion.toulu.bot.command;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Bot 命令枚举测试
 * 
 * <AUTHOR> 4.0 sonnet
 */
public class BotCommandTest {
    
    @Test
    public void testFromString() {
        // 测试有效命令
        assertEquals(BotCommand.START, BotCommand.fromString("/start"));
        assertEquals(BotCommand.HELP, BotCommand.fromString("/help"));
        assertEquals(BotCommand.STATUS, BotCommand.fromString("/status"));
        
        // 测试带参数的命令
        assertEquals(BotCommand.START, BotCommand.fromString("/start param1 param2"));
        
        // 测试大小写不敏感
        assertEquals(BotCommand.START, BotCommand.fromString("/START"));
        assertEquals(BotCommand.HELP, BotCommand.fromString("/Help"));
        
        // 测试无效命令
        assertEquals(BotCommand.UNKNOWN, BotCommand.fromString("/invalid"));
        assertEquals(BotCommand.UNKNOWN, BotCommand.fromString(""));
        assertEquals(BotCommand.UNKNOWN, BotCommand.fromString(null));
    }
    
    @Test
    public void testCommandProperties() {
        // 测试 START 命令属性
        BotCommand start = BotCommand.START;
        assertEquals("/start", start.getCommand());
        assertEquals("开始使用机器人", start.getDescription());
        assertFalse(start.isAdminOnly());
        assertTrue(start.isShowInHelp());
        assertTrue(start.isValid());
        
        // 测试 STATUS 命令属性
        BotCommand status = BotCommand.STATUS;
        assertEquals("/status", status.getCommand());
        assertTrue(status.isAdminOnly());
        assertTrue(status.isShowInHelp());
        assertTrue(status.isValid());
        
        // 测试 UNKNOWN 命令属性
        BotCommand unknown = BotCommand.UNKNOWN;
        assertFalse(unknown.isValid());
    }
    
    @Test
    public void testGetHelpCommands() {
        BotCommand[] helpCommands = BotCommand.getHelpCommands();
        
        // 确保包含基本命令
        assertTrue(java.util.Arrays.asList(helpCommands).contains(BotCommand.START));
        assertTrue(java.util.Arrays.asList(helpCommands).contains(BotCommand.HELP));
        
        // 确保不包含 UNKNOWN 命令
        assertFalse(java.util.Arrays.asList(helpCommands).contains(BotCommand.UNKNOWN));
    }
    
    @Test
    public void testGetUserCommands() {
        BotCommand[] userCommands = BotCommand.getUserCommands();
        
        // 确保只包含非管理员命令
        for (BotCommand command : userCommands) {
            assertFalse(command.isAdminOnly(), 
                "用户命令列表不应包含管理员命令: " + command.getCommand());
        }
    }
    
    @Test
    public void testGetAdminCommands() {
        BotCommand[] adminCommands = BotCommand.getAdminCommands();
        
        // 确保只包含管理员命令
        for (BotCommand command : adminCommands) {
            assertTrue(command.isAdminOnly(), 
                "管理员命令列表应只包含管理员命令: " + command.getCommand());
        }
    }
}
