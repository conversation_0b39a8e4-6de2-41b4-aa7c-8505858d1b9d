# 开发环境配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 开发环境数据库 - 可以是本地数据库或开发服务器
    url: *******************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: 123456

server:
  port: 18888

# 开发环境日志配置
logging:
  level:
    root: INFO
    me.orion: DEBUG
    org.springframework.web: DEBUG
    org.mybatis: DEBUG

# Telegram 测试机器人配置
telegram:
  token: **********************************************
  username: locahl_test1123123_bot
