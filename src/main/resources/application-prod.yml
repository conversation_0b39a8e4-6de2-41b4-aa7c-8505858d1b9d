# 生产环境配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 生产环境数据库
    url: ******************************************************************************************************************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:mysql_xDhMmz}


server:
  port: ${SERVER_PORT:18888}
  # 生产环境服务器配置
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    max-connections: 8192
    accept-count: 100
    connection-timeout: 20000

# 生产环境日志配置
logging:
  level:
    root: WARN
    me.orion: INFO
    org.springframework: WARN
    org.mybatis: WARN

# 生产环境 Telegram 机器人配置
telegram:
  token: ${TELEGRAM_TOKEN:**********************************************}
  username: ${TELEGRAM_USERNAME:locahl_test1123123_bot}