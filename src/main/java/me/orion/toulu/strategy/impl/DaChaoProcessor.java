package me.orion.toulu.strategy.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import me.orion.toulu.strategy.HarDataProcessor;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class Da<PERSON>haoProcessor implements HarDataProcessor {

    private static final String DaChao_URL = "https://m.aihoge.com/api";


    @Override
    public boolean canProcess(String url) {
        return url.contains(DaChao_URL);
    }

    @Override
    public String processData(JSONObject harData) {
        // 解析请求头
        JSONArray headers = harData.getJSONObject("request").getJSONArray("headers");
        for (Object header : headers) {
            JSONObject headerObj = JSONObject.from(header);
            String name = headerObj.getString("name");
            String value = headerObj.getString("value");
            if (Objects.equals(name, "member")) {
                return value;
            }
        }
        return null;
    }

    @Override
    public String getPlatformName() {
        return "大潮";
    }

    @Override
    public String getSupportedUrl() {
        return DaChao_URL + "/*";
    }

    @Override
    public String getDescription() {
        return "大潮member";
    }
}
