package me.orion.toulu.strategy.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import me.orion.toulu.common.CkUtils;
import me.orion.toulu.strategy.HarDataProcessor;
import org.springframework.stereotype.Component;

/**
 * 快手平台HAR数据处理器
 *
 * <AUTHOR> 4.0 sonnet
 */
@Component
public class XiFanProcessor implements HarDataProcessor {

    private static final String XIFAN_URL = "https://tube.e.kuaishou.com/rest/e/tube/inspire/task/report";
    private static final String LINK = "#";

    @Override
    public boolean canProcess(String url) {
        return XIFAN_URL.equals(url);
    }

    @Override
    public String processData(JSONObject harData) {
        String Cookie = "";
        String UserAgent = "";
        String BrowserUa = "";
        String SystemUa = "";
        String message = "";

        // 解析请求头
        JSONArray headers = harData.getJSONObject("request").getJSONArray("headers");
        for (Object header : headers) {
            JSONObject headerObj = JSONObject.from(header);
            String name = headerObj.getString("name");
            String value = headerObj.getString("value");

            switch (name) {
                case "Cookie" -> Cookie = value;
                case "User-Agent" -> UserAgent = value;
                case "SystemUa" -> SystemUa = value;
                case "BrowserUa" -> BrowserUa = value;
            }
        }

        // 解析POST数据
        String text = harData.getJSONObject("request").getJSONObject("postData").getString("text");
        JSONObject jsonObject = JSONObject.parseObject(text);
        message = CkUtils.cleanEncodedData(jsonObject.getString("message"));

        return Cookie + LINK + BrowserUa + LINK + UserAgent + LINK + SystemUa + LINK + "com.kwai.xifan" + LINK + message;
    }

    @Override
    public String getPlatformName() {
        return "喜番";
    }

    @Override
    public String getSupportedUrl() {
        return XIFAN_URL;
    }

    @Override
    public String getDescription() {
        return "喜番抓包IOS";
    }
}