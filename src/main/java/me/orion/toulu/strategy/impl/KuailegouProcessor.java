package me.orion.toulu.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import me.orion.toulu.strategy.HarDataProcessor;

public class <PERSON>ailegouProcessor implements HarDataProcessor {

    private static final String Kuailegou_URL = "https://game.happigo.com/App/zh202506?cps_id=10000037&platform=wxlite";

    @Override
    public boolean canProcess(String url) {
        return url.contains(Kuailegou_URL);
    }

    @Override
    public String processData(JSONObject harData) {
        System.out.println(harData);
        return "";
    }

    @Override
    public String getPlatformName() {
        return "快乐购-果园";
    }

    @Override
    public String getSupportedUrl() {
        return Kuailegou_URL + "/*";
    }

    @Override
    public String getDescription() {
        return "快乐购-果园";
    }
}
