package me.orion.toulu.strategy;

import com.alibaba.fastjson2.JSONObject;

/**
 * HAR数据处理策略接口
 * <AUTHOR> 4.0 sonnet
 */
public interface HarDataProcessor {
    /**
     * 判断是否能处理该URL
     */
    boolean canProcess(String url);
    
    /**
     * 处理HAR数据
     */
    String processData(JSONObject harData);
    
    /**
     * 获取平台名称
     */
    String getPlatformName();
    
    /**
     * 获取支持的URL模式
     */
    String getSupportedUrl();
    
    /**
     * 获取处理器描述
     */
    String getDescription();
}