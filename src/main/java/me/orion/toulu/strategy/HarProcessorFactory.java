package me.orion.toulu.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.stream.Collectors;

/**
 * HAR处理器工厂
 * <AUTHOR> 4.0 sonnet
 */
@Component
public class HarProcessorFactory {
    
    @Autowired
    private List<HarDataProcessor> processors;
    
    /**
     * 根据URL获取对应的处理器
     */
    public HarDataProcessor getProcessor(String url) {
        return processors.stream()
                .filter(processor -> processor.canProcess(url))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取所有处理器
     */
    public List<HarDataProcessor> getAllProcessors() {
        return processors;
    }
    
    /**
     * 获取支持的平台列表
     */
    public String getSupportedPlatforms() {
        return processors.stream()
                .map(HarDataProcessor::getPlatformName)
                .collect(Collectors.joining("、"));
    }
}