package me.orion.toulu.bot;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Telegram Bot 配置类
 *
 * <AUTHOR> 4.0 sonnet
 */
@Configuration
@ConfigurationProperties(prefix = "telegram")
@Data
public class BotConfig {

    /**
     * Bot <PERSON>（必需）
     */
    private String token;

    /**
     * Bot 用户名（必需）
     */
    private String username;
}