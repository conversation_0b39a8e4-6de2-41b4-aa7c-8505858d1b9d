package me.orion.toulu.bot;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.bots.TelegramLongPollingBot;
import org.telegram.telegrambots.meta.TelegramBotsApi;
import org.telegram.telegrambots.meta.api.objects.Chat;
import org.telegram.telegrambots.meta.api.objects.Message;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.updatesreceivers.DefaultBotSession;

import java.util.List;

/**
 * Telegram Bot 主类
 * 负责接收和分发消息，具备完整的错误处理和安全检查
 *
 * <AUTHOR> 4.0 sonnet
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MyTeleBot extends Telegram<PERSON>ongPollingBot {

    private final BotConfig botConfig;


    @PostConstruct
    public void init() {
        try {
            // 注册 Bot
            TelegramBotsApi telegramBotsApi = new TelegramBotsApi(DefaultBotSession.class);
            telegramBotsApi.registerBot(this);

            log.info("🤖 Telegram Bot [{}] 启动成功", botConfig.getUsername());

        } catch (TelegramApiException e) {
            String errorMsg = String.format("Telegram Bot 启动失败: %s", e.getMessage());
            log.error(errorMsg, e);
        } catch (Exception e) {
            String errorMsg = String.format("Bot 初始化过程中发生未知错误: %s", e.getMessage());
            log.error(errorMsg, e);
        }
    }

    @Override
    public void onUpdateReceived(Update update) {
        if (update.hasMessage()) {
            if (update.getMessage().hasText()) {
                Chat chat = update.getMessage().getChat();
                Message message = update.getMessage();
                log.info("【TELEGRAM】[{}]-[{}]发送消息：{}", chat.getTitle(), message.getFrom().getUserName(), message.getText());
            }
        }
    }


    @Override
    public void onUpdatesReceived(List<Update> updates) {
        super.onUpdatesReceived(updates);
    }

    @Override
    public String getBotUsername() {
        return botConfig.getUsername();
    }

    @Override
    public String getBotToken() {
        return botConfig.getToken();
    }
}