package me.orion.toulu.service;

import com.alibaba.fastjson2.JSON;
import me.orion.toulu.entity.HarFile;
import me.orion.toulu.entity.HarEntry;
import org.springframework.stereotype.Service;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Service
public class HarParseService {
    
    public Map<String, Object> parseHarFile(InputStream inputStream) throws Exception {
        // 读取流内容
        String content = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
        
        // 解析JSON
        HarFile harFile = JSON.parseObject(content, HarFile.class);
        
        // 提取关键信息
        Map<String, Object> result = new HashMap<>();
        List<HarEntry> entries = harFile.getLog().getEntries();
        
        result.put("totalRequests", entries.size());
        result.put("creator", harFile.getLog().getCreator());
        result.put("version", harFile.getLog().getVersion());
        
        // 统计请求方法
        Map<String, Integer> methodStats = new HashMap<>();
        // 统计状态码
        Map<String, Integer> statusStats = new HashMap<>();
        
        for (HarEntry entry : entries) {
            String method = entry.getRequest().getMethod();
            methodStats.put(method, methodStats.getOrDefault(method, 0) + 1);
            
            String status = String.valueOf(entry.getResponse().getStatus());
            statusStats.put(status, statusStats.getOrDefault(status, 0) + 1);
        }
        
        result.put("methodStats", methodStats);
        result.put("statusStats", statusStats);
        result.put("entries", entries);
        
        return result;
    }
}