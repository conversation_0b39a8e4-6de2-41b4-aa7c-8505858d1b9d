package me.orion.toulu.controller;

import com.alibaba.fastjson2.JSONObject;
import me.orion.toulu.entity.MsgEntity;
import me.orion.toulu.entity.R;
import me.orion.toulu.service.IMsgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@RestController
@RequestMapping("/msg")
public class MsgController {

    @Autowired
    private IMsgService msgService;


    @GetMapping("/info")
    private R<JSONObject> info() {
        JSONObject jsonObject = new JSONObject();
        MsgEntity msg = msgService.getById(1);
        jsonObject.put("title", msg.getTitle());
        jsonObject.put("context", msg.getContext());
        return R.success(jsonObject);
    }

}
