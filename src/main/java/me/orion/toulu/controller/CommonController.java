package me.orion.toulu.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import me.orion.toulu.entity.R;
import me.orion.toulu.service.HarParseService;
import me.orion.toulu.strategy.HarDataProcessor;
import me.orion.toulu.strategy.HarProcessorFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/common")
@CrossOrigin(origins = "*")
public class CommonController {

    @Autowired
    private HarParseService harParseService;

    @Autowired
    private HarProcessorFactory harProcessorFactory;

    @PostMapping("/upload")
    public R<String> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件是否为空
            if (file.isEmpty()) {
                return R.clientError("文件不能为空");
            }

            // 验证文件后缀
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".har")) {
                return R.clientError("只支持上传HAR格式文件");
            }

            // 文件大小限制 (10MB)
            if (file.getSize() > 10 * 1024 * 1024) {
                return R.clientError("文件大小不能超过10MB");
            }

            // 解析HAR文件
            JSONArray entries = JSONArray.from(harParseService.parseHarFile(file.getInputStream()).get("entries"));
            if (entries.isEmpty()) {
                return R.error("HAR文件中没有找到请求记录");
            }

            JSONObject data = JSONObject.from(entries.get(0));
            String url = data.getJSONObject("request").getString("url");

            // 使用工厂获取对应的处理器
            HarDataProcessor processor = harProcessorFactory.getProcessor(url);
            if (processor == null) {
                return R.error("不支持的抓包地址，当前支持的平台：" + harProcessorFactory.getSupportedPlatforms());
            }

            // 处理数据
            String result = processor.processData(data);
            return R.success(result, processor.getPlatformName() + "数据解析成功");

        } catch (IOException e) {
            return R.error("文件读取失败: " + e.getMessage());
        } catch (Exception e) {
            return R.error("HAR文件解析失败: " + e.getMessage());
        }
    }
}
