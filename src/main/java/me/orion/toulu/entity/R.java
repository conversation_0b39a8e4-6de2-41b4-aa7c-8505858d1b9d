package me.orion.toulu.entity;

import lombok.Data;
import java.io.Serializable;

/**
 * 统一响应体
 *
 * @param <T> 响应数据类型
 * <AUTHOR> 4.0 sonnet
 */
@Data
public class R<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成功状态码
     */
    public static final Integer SUCCESS_CODE = 200;

    /**
     * 服务器错误状态码
     */
    public static final Integer ERROR_CODE = 500;

    /**
     * 客户端错误状态码
     */
    public static final Integer CLIENT_ERROR_CODE = 400;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 私有构造函数
     */
    private R() {
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 私有构造函数
     */
    private R(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return SUCCESS_CODE.equals(this.code);
    }

    /**
     * 成功响应（无数据）
     */
    public static <T> R<T> success() {
        return new R<>(SUCCESS_CODE, "操作成功", null);
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> R<T> success(T data) {
        return new R<>(SUCCESS_CODE, "操作成功", data);
    }

    /**
     * 成功响应（自定义消息）
     */
    public static <T> R<T> success(String message) {
        return new R<>(SUCCESS_CODE, message, null);
    }

    /**
     * 成功响应（带数据和自定义消息）
     */
    public static <T> R<T> success(T data, String message) {
        return new R<>(SUCCESS_CODE, message, data);
    }

    /**
     * 错误响应（默认消息）
     */
    public static <T> R<T> error() {
        return new R<>(ERROR_CODE, "操作失败", null);
    }

    /**
     * 错误响应（自定义消息）
     */
    public static <T> R<T> error(String message) {
        return new R<>(ERROR_CODE, message, null);
    }

    /**
     * 错误响应（自定义状态码和消息）
     */
    public static <T> R<T> error(Integer code, String message) {
        return new R<>(code, message, null);
    }

    /**
     * 客户端错误响应
     */
    public static <T> R<T> clientError(String message) {
        return new R<>(CLIENT_ERROR_CODE, message, null);
    }
}
