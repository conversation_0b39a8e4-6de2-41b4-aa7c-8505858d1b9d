package me.orion.toulu.common;

public class CkUtils {
    /**
     * 清理编码数据，移除换行符和其他格式字符
     * @param rawData 原始数据
     * @return 清理后的数据
     */
    public static String cleanEncodedData(String rawData) {
        // 移除 \\r\\n 字符
        String cleaned = rawData.replace("\\r\\n", "");

        // 移除真实的换行符
        cleaned = cleaned.replace("\r\n", "");
        cleaned = cleaned.replace("\n", "");
        cleaned = cleaned.replace("\r", "");

        // 移除多余的空格
        cleaned = cleaned.trim();

        return cleaned;
    }
}
