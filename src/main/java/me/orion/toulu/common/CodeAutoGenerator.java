package me.orion.toulu.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.sql.Types;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CodeAutoGenerator {

    public static void main(String[] args) {
        String outputDir = System.getProperty("user.dir") + "/src/main/java";
        String xmlDir = System.getProperty("user.dir") + "/src/main/resources";
        List<String> tables = new ArrayList<>();
        tables.add("tb_msg");
        FastAutoGenerator.create("********************************************************************************************************************************************************************************************", "root", "mysql_xDhMmz")
                .globalConfig(builder -> {
                    builder.author("yqx") // 设置作者
                            .outputDir(outputDir); // 指定输出目录
                })
                .dataSourceConfig(builder -> builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                    int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                    if (typeCode == Types.SMALLINT || typeCode == Types.TINYINT) {
                        // 自定义类型转换
                        return DbColumnType.INTEGER;
                    }
                    return typeRegistry.getColumnType(metaInfo);

                }))
                .packageConfig(builder -> {
                    builder.parent("me.orion.toulu")
                            .entity("entity")
                            .controller("controller")
                            .service("service")
                            .serviceImpl("service.impl")
                            .mapper("mapper")
                            .pathInfo(Collections.singletonMap(OutputFile.xml, xmlDir + "/mapper")); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude(tables) // 设置需要生成的表名
                            .addTablePrefix("tb_").entityBuilder().formatFileName("%sEntity").enableLombok().idType(IdType.AUTO)
                            .mapperBuilder().enableMapperAnnotation(); // 设置过滤表前缀
                })
                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}
