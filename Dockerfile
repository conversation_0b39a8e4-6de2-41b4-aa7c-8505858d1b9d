# =============================================
# STAGE 1: Build aplication using Maven & JDK
# =============================================
# 使用官方的 Maven 镜像作为构建环境，该镜像包含了 JDK 17
FROM maven:3.9-eclipse-temurin-17 AS builder

# 设置工作目录
WORKDIR /app

# 1. 仅复制 pom.xml，利用 Docker 缓存机制
# 只要 pom.xml 没有变化，Docker 就不会重新下载依赖，从而加速构建
COPY pom.xml .
RUN mvn dependency:go-offline

# 2. 复制所有源代码
COPY src ./src

# 3. 执行 Maven 打包命令
# -DskipTests 会跳过测试，这在构建镜像时是标准做法
RUN mvn package -DskipTests

# =============================================
# STAGE 2: Create a minimal production image
# =============================================
# 使用一个仅包含 JRE 的、更小的基础镜像来运行应用
FROM eclipse-temurin:17-jre-jammy

# 设置工作目录
WORKDIR /app

# 从 builder 阶段复制构建好的 JAR 文件到当前阶段
# pom.xml 中 <finalName> 定义了 JAR 的名字为 monitor.jar
COPY --from=builder /app/target/toulu.jar .

# 暴露应用端口，这与你的 application.yaml 中 server.port: 8000 一致
EXPOSE 18888

# 容器启动时执行的命令
ENTRYPOINT ["java","-Xmx512m", "-jar", "toulu.jar"]
